export interface ZaloMessage {
  message_id: string;
  from_id: string;
  to_id: string;
  message: {
    text?: string;
    attachment?: ZaloAttachment;
  };
  timestamp: number;
}

export interface ZaloAttachment {
  type: 'image' | 'file' | 'audio' | 'video' | 'location';
  payload: {
    url?: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
  };
}

export interface ZaloUser {
  user_id: string;
  display_name: string;
  avatar?: string;
}

export interface ZaloWebhookEvent {
  app_id: string;
  user_id_by_app: string;
  event_name: string;
  message?: ZaloMessage;
  sender?: ZaloUser;
  recipient?: ZaloUser;
  timestamp: number;
}

export interface ZaloResponse {
  error: number;
  message: string;
  data?: any;
}
