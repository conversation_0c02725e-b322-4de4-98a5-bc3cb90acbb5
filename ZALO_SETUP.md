# Hướng dẫn cài đặt Zalo Bot với zca-js

## 1. <PERSON><PERSON>y Cookie Zalo

### Cách 1: Lấy cookie từ trình duyệt
1. Mở trình duyệt và đăng nhập vào https://chat.zalo.me
2. Mở Developer Tools (F12)
3. Vào tab Application/Storage → Cookies → https://chat.zalo.me
4. Copy toàn bộ cookies (hoặc chỉ cần các cookie quan trọng như `zpw_sek`, `zpsid`, etc.)
5. Dán vào file `.env` ở dòng `ZALO_COOKIE=`

### Cách 2: Sử dụng QR Code (Khuyến nghị)
Nếu không muốn sử dụng cookie, bạn có thể sử dụng QR code login:

```typescript
// Trong ZaloBot.ts, thay thế phần login bằng:
await this.api.loginWithQR();
```

## 2. <PERSON><PERSON>u hình Environment Variables

Cập nhật file `.env`:

```env
# Zalo Configuration
ZALO_COOKIE=your_zalo_cookie_here
ZALO_USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36

# Application Configuration
NODE_ENV=development
PORT=3000
LOG_LEVEL=info
LOG_FORMAT=combined

# Webhook Configuration (nếu cần)
WEBHOOK_URL=http://localhost:3000/webhook
WEBHOOK_SECRET=your_webhook_secret_here
```

## 3. Chạy Bot

```bash
# Development mode
yarn dev

# Production mode
yarn build
yarn start
```

## 4. Tính năng hiện tại

Bot hiện tại có các tính năng:
- ✅ Nhận và trả lời tin nhắn text
- ✅ Echo bot (lặp lại tin nhắn)
- ✅ Xử lý lệnh "hello", "hi", "help"
- ✅ Gửi sticker
- ✅ Gửi tin nhắn có định dạng (bold, italic, màu sắc)
- ✅ Xử lý tin nhắn nhóm và cá nhân

## 5. Tùy chỉnh Bot

Bạn có thể tùy chỉnh logic xử lý tin nhắn trong file `src/services/ZaloBot.ts`:

```typescript
private async handleTextMessage(message: any): Promise<void> {
  const messageText = message.data.content;
  const threadId = message.threadId;
  const threadType = message.type;

  // Thêm logic xử lý tùy chỉnh ở đây
  if (messageText.includes('weather')) {
    await this.sendTextMessage(threadId, 'Hôm nay trời đẹp!', threadType);
  }
  // ... thêm các điều kiện khác
}
```

## 6. Lưu ý quan trọng

- ⚠️ Cookie có thể hết hạn, cần cập nhật định kỳ
- ⚠️ Không chia sẻ cookie với người khác
- ⚠️ Sử dụng QR code login an toàn hơn cho production
- ⚠️ Tuân thủ Terms of Service của Zalo

## 7. Troubleshooting

### Lỗi "Missing required Zalo configuration"
- Kiểm tra file `.env` có tồn tại không
- Đảm bảo `ZALO_COOKIE` được set đúng

### Lỗi login
- Cookie có thể đã hết hạn, cần lấy cookie mới
- Thử sử dụng QR code login thay thế

### Bot không phản hồi
- Kiểm tra logs để xem có lỗi gì không
- Đảm bảo bot đã đăng nhập thành công
