import { Config } from '../types/config';

// Declare Node.js globals
declare const process: any;
declare const parseInt: any;

export const config: Config = {
  app: {
    name: 'Zalo Chatbot',
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    port: parseInt(process.env.PORT || '3000', 10),
  },
  zalo: {
    appId: process.env.ZALO_APP_ID || '',
    secretKey: process.env.ZALO_SECRET_KEY || '',
    oaId: process.env.ZALO_OA_ID || '',
    webhookUrl: process.env.ZALO_WEBHOOK_URL || '',
  },
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'combined',
  },
};

// Validate required environment variables
export function validateConfig(): void {
  const requiredEnvVars = ['ZALO_APP_ID', 'ZALO_SECRET_KEY', 'ZALO_OA_ID'];

  const missingVars = requiredEnvVars.filter((varName: string) => !process.env[varName]);

  if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }
}
