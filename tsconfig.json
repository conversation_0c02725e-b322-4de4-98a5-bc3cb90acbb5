{"compilerOptions": {"target": "ES2022", "module": "CommonJS", "lib": ["ES2022"], "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "outDir": "./dist", "rootDir": "./src", "removeComments": true, "strict": false, "skipLibCheck": true, "declaration": true, "sourceMap": true, "baseUrl": "./", "paths": {"@/*": ["src/*"], "@/types/*": ["src/types/*"], "@/utils/*": ["src/utils/*"], "@/services/*": ["src/services/*"], "@/config/*": ["src/config/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"esm": false}}