# Zalo Chatbot - TypeScript

Dự án Chatbot Zalo được xây dựng với TypeScript, Node.js và Yarn.

## 🚀 Tính năng

- ✅ TypeScript với cấu hình tối ưu
- ✅ Prettier cho code formatting
- ✅ Nodemon cho development hot reload
- ✅ Path mapping (@/* aliases)
- ✅ Cấu trúc dự án tối ưu và dễ mở rộng
- ✅ Environment configuration
- ✅ Logging system đơn giản
- ✅ Zalo webhook handling structure

## 📁 Cấu trúc dự án

```
src/
├── config/          # Cấu hình ứng dụng
├── controllers/     # Controllers xử lý request
├── middleware/      # Middleware functions
├── services/        # Business logic services
├── types/          # TypeScript type definitions
├── utils/          # Utility functions
└── index.ts        # Entry point
```

## 🛠️ Cài đặt

1. Clone repository:
```bash
git clone <repository-url>
cd chatbot-zalo
```

2. Cài đặt dependencies:
```bash
yarn install
```

3. Tạo file environment:
```bash
cp .env.example .env
```

4. Cấu hình environment variables trong `.env`:
```env
ZALO_APP_ID=your_zalo_app_id
ZALO_SECRET_KEY=your_zalo_secret_key
ZALO_OA_ID=your_zalo_oa_id
ZALO_WEBHOOK_URL=https://your-domain.com/webhook
```

## 🏃‍♂️ Chạy dự án

### Development
```bash
yarn dev
```

### Production
```bash
yarn build
yarn start
```

## 📝 Scripts có sẵn

- `yarn dev` - Chạy development server với hot reload
- `yarn build` - Build production
- `yarn start` - Chạy production server
- `yarn clean` - Xóa thư mục dist
- `yarn type-check` - Kiểm tra TypeScript types

- `yarn format` - Format code với Prettier
- `yarn format:check` - Kiểm tra code formatting
- `yarn check-all` - Chạy tất cả checks (type, format)

## 🔧 Development

### Code Quality
Dự án sử dụng:
- **Prettier** cho code formatting
- **TypeScript** với cấu hình tối ưu
- **Nodemon** cho development hot reload

### Path Aliases
Sử dụng path aliases để import dễ dàng:
```typescript
import { config } from '@/config/config';
import { ZaloBot } from '@/services/ZaloBot';
import { logger } from '@/utils/logger';
```

## 📚 Tài liệu

- [Zalo Official API](https://developers.zalo.me/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Node.js Documentation](https://nodejs.org/docs/)

## 🤝 Contributing

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Tạo Pull Request

## 📄 License

MIT License
