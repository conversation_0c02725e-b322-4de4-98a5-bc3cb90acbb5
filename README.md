# Zalo Chatbot - TypeScript với zca-js

Dự án Chatbot Zalo được xây dựng với TypeScript, Node.js, Yarn và thư viện zca-js.

## 🚀 Tính năng

- ✅ TypeScript với cấu hình tối ưu
- ✅ Tích hợp zca-js để kết nối Zalo
- ✅ Đăng nhập bằng Cookie hoặc QR Code
- ✅ Xử lý tin nhắn text, sticker, hình ảnh
- ✅ Gửi tin nhắn có định dạng (bold, italic, màu sắc)
- ✅ Hỗ trợ tin nhắn nhóm và cá nhân
- ✅ Prettier cho code formatting
- ✅ Nodemon cho development hot reload
- ✅ Cấu trúc dự án tối ưu và dễ mở rộng
- ✅ Environment configuration
- ✅ Logging system đầy đủ
- ✅ Các lệnh bot thông minh (/help, /style, /info, etc.)

## 📁 Cấu trúc dự án

```
src/
├── config/          # <PERSON><PERSON><PERSON> hình <PERSON>ng dụng
├── controllers/     # Controllers xử lý request
├── middleware/      # Middleware functions
├── services/        # Business logic services (ZaloBot)
├── types/          # TypeScript type definitions
├── utils/          # Utility functions (logger)
├── scripts/        # Utility scripts (QR login)
├── examples/       # Ví dụ tính năng nâng cao
└── index.ts        # Entry point
```

## 🛠️ Cài đặt

1. Clone repository:
```bash
git clone <repository-url>
cd chatbot-zalo
```

2. Cài đặt dependencies:
```bash
yarn install
```

3. File `.env` đã được tạo sẵn, bạn cần cấu hình:

### Cách 1: Đăng nhập bằng QR Code (Khuyến nghị)
```bash
yarn qr-login
```
Sau đó copy cookie từ kết quả vào file `.env`

### Cách 2: Lấy cookie thủ công
1. Đăng nhập vào https://chat.zalo.me
2. Mở Developer Tools (F12) → Application → Cookies
3. Copy cookie và dán vào `.env`:

```env
ZALO_COOKIE=your_zalo_cookie_here
ZALO_USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
NODE_ENV=development
PORT=3000
```

## 🏃‍♂️ Chạy dự án

### Development
```bash
yarn dev
```

### Production
```bash
yarn build
yarn start
```

## 📝 Scripts có sẵn

### Bot Commands
- `yarn qr-login` - Đăng nhập bằng QR code và lấy cookie
- `yarn dev` - Chạy development server với hot reload
- `yarn build` - Build production
- `yarn start` - Chạy production server

### Development Tools
- `yarn clean` - Xóa thư mục dist
- `yarn type-check` - Kiểm tra TypeScript types
- `yarn format` - Format code với Prettier
- `yarn format:check` - Kiểm tra code formatting
- `yarn check-all` - Chạy tất cả checks (type, format)

## 🔧 Development

### Code Quality
Dự án sử dụng:
- **Prettier** cho code formatting
- **TypeScript** với cấu hình tối ưu
- **Nodemon** cho development hot reload

### Path Aliases
Sử dụng path aliases để import dễ dàng:
```typescript
import { config } from '@/config/config';
import { ZaloBot } from '@/services/ZaloBot';
import { logger } from '@/utils/logger';
```

## 🤖 Tính năng Bot

### Lệnh cơ bản:
- `/help` - Hiển thị trợ giúp
- `/info` - Thông tin bot
- `/time` - Thời gian hiện tại
- `/style` - Ví dụ tin nhắn có định dạng
- `/urgent` - Tin nhắn quan trọng
- `/sticker [id]` - Gửi sticker

### Tin nhắn thông minh:
- Bot tự động phản hồi "hello", "hi", "xin chào"
- Xử lý "tạm biệt", "bye"
- Phản hồi "cảm ơn", "thank you"
- Thông tin thời tiết khi gửi "thời tiết"

## 📚 Tài liệu

- [zca-js Documentation](https://tdung.gitbook.io/zca-js/)
- [Zalo Official API](https://developers.zalo.me/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Node.js Documentation](https://nodejs.org/docs/)

## 🔧 Tùy chỉnh Bot

Xem file `src/examples/advanced-features.ts` để tham khảo các tính năng nâng cao:
- Tin nhắn có định dạng
- Xử lý lệnh phức tạp
- Phản hồi thông minh
- Tích hợp API bên ngoài

## 🤝 Contributing

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Tạo Pull Request

## 📄 License

MIT License
