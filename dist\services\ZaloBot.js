"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ZaloBot = void 0;
const logger_1 = require("../utils/logger");
class ZaloBot {
    config;
    isInitialized = false;
    constructor(config) {
        this.config = config;
    }
    async initialize() {
        try {
            logger_1.logger.info('Initializing Zalo Bot...');
            this.validateConfig();
            this.isInitialized = true;
            logger_1.logger.info('Zalo Bot initialized successfully');
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize Zalo Bot:', error);
            throw error;
        }
    }
    validateConfig() {
        const { appId, secretKey, oaId } = this.config;
        if (!appId || !secretKey || !oaId) {
            throw new Error('Missing required Zalo configuration');
        }
    }
    async handleWebhookEvent(event) {
        if (!this.isInitialized) {
            throw new Error('ZaloBot is not initialized');
        }
        try {
            logger_1.logger.info(`Received webhook event: ${event.event_name}`);
            switch (event.event_name) {
                case 'user_send_text':
                    await this.handleTextMessage(event);
                    break;
                case 'user_send_image':
                    await this.handleImageMessage(event);
                    break;
                default:
                    logger_1.logger.warn(`Unhandled event type: ${event.event_name}`);
            }
        }
        catch (error) {
            logger_1.logger.error('Error handling webhook event:', error);
            throw error;
        }
    }
    async handleTextMessage(event) {
        if (!event.message)
            return;
        const message = event.message;
        const userId = event.user_id_by_app;
        logger_1.logger.info(`Text message from ${userId}: ${message.message.text}`);
        await this.sendTextMessage(userId, `Echo: ${message.message.text}`);
    }
    async handleImageMessage(event) {
        if (!event.message)
            return;
        const userId = event.user_id_by_app;
        logger_1.logger.info(`Image message from ${userId}`);
        await this.sendTextMessage(userId, 'Tôi đã nhận được hình ảnh của bạn!');
    }
    async sendTextMessage(userId, text) {
        try {
            logger_1.logger.info(`Sending text message to ${userId}: ${text}`);
            logger_1.logger.info('Message sent successfully');
        }
        catch (error) {
            logger_1.logger.error('Failed to send message:', error);
            throw error;
        }
    }
}
exports.ZaloBot = ZaloBot;
//# sourceMappingURL=ZaloBot.js.map