"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const ZaloBot_1 = require("./services/ZaloBot");
const config_1 = require("./config/config");
const logger_1 = require("./utils/logger");
async function main() {
    try {
        logger_1.logger.info('🚀 Starting Zalo Chatbot...');
        const bot = new ZaloBot_1.ZaloBot(config_1.config.zalo);
        await bot.initialize();
        logger_1.logger.info('✅ Zalo Chatbot started successfully!');
    }
    catch (error) {
        logger_1.logger.error('❌ Failed to start Zalo Chatbot:', error);
        process.exit(1);
    }
}
process.on('SIGINT', () => {
    logger_1.logger.info('🛑 Shutting down gracefully...');
    process.exit(0);
});
process.on('SIGTERM', () => {
    logger_1.logger.info('🛑 Shutting down gracefully...');
    process.exit(0);
});
main().catch(error => {
    logger_1.logger.error('💥 Unhandled error:', error);
    process.exit(1);
});
//# sourceMappingURL=index.js.map