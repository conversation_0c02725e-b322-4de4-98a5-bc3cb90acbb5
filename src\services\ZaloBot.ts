import { ZaloConfig, ZaloWebhookEvent, ZaloMessage } from '../types';
import { logger } from '../utils/logger';

export class ZaloBot {
  private config: ZaloConfig;
  private isInitialized = false;

  constructor(config: ZaloConfig) {
    this.config = config;
  }

  async initialize(): Promise<void> {
    try {
      logger.info('Initializing Zalo Bot...');

      // Validate configuration
      this.validateConfig();

      // Initialize zca-js or other Zalo SDK here
      // const { ZCA } = require('zca-js');
      // this.zca = new ZCA(this.config);

      this.isInitialized = true;
      logger.info('Zalo Bot initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Zalo Bot:', error);
      throw error;
    }
  }

  private validateConfig(): void {
    const { appId, secretKey, oaId } = this.config;

    if (!appId || !secretKey || !oaId) {
      throw new Error('Missing required Zalo configuration');
    }
  }

  async handleWebhookEvent(event: ZaloWebhookEvent): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('ZaloBot is not initialized');
    }

    try {
      logger.info(`Received webhook event: ${event.event_name}`);

      switch (event.event_name) {
        case 'user_send_text':
          await this.handleTextMessage(event);
          break;
        case 'user_send_image':
          await this.handleImageMessage(event);
          break;
        default:
          logger.warn(`Unhandled event type: ${event.event_name}`);
      }
    } catch (error) {
      logger.error('Error handling webhook event:', error);
      throw error;
    }
  }

  private async handleTextMessage(event: ZaloWebhookEvent): Promise<void> {
    if (!event.message) return;

    const message = event.message;
    const userId = event.user_id_by_app;

    logger.info(`Text message from ${userId}: ${message.message.text}`);

    // Implement your text message handling logic here
    await this.sendTextMessage(userId, `Echo: ${message.message.text}`);
  }

  private async handleImageMessage(event: ZaloWebhookEvent): Promise<void> {
    if (!event.message) return;

    const userId = event.user_id_by_app;
    logger.info(`Image message from ${userId}`);

    // Implement your image message handling logic here
    await this.sendTextMessage(userId, 'Tôi đã nhận được hình ảnh của bạn!');
  }

  async sendTextMessage(userId: string, text: string): Promise<void> {
    try {
      logger.info(`Sending text message to ${userId}: ${text}`);

      // Implement actual message sending using zca-js or Zalo API
      // await this.zca.sendMessage(userId, { text });

      logger.info('Message sent successfully');
    } catch (error) {
      logger.error('Failed to send message:', error);
      throw error;
    }
  }
}
